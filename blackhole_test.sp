#pragma semicolon 1
#pragma newdecls required

#include <sourcemod>
#include <sdktools>

public Plugin myinfo = {
    name = "Black Hole Test",
    description = "Test black hole functionality",
    author = "Test",
    version = "1.0",
    url = ""
}

public void OnPluginStart()
{
    RegAdminCmd("sm_testblackhole", Cmd_TestBlackHole, ADMFLAG_ROOT, "Test black hole creation");
}

public Action Cmd_TestBlackHole(int client, int args)
{
    if (client <= 0 || !IsClientInGame(client))
        return Plugin_Handled;
        
    float pos[3];
    GetClientAbsOrigin(client, pos);
    
    PrintToChat(client, "Creating test black hole at position: %.1f, %.1f, %.1f", pos[0], pos[1], pos[2]);
    
    // 测试创建一个简单的实体
    int entity = CreateEntityByName("prop_dynamic");
    if (entity != -1)
    {
        SetEntityModel(entity, "models/props/cs_militia/silo_01.mdl");
        DispatchSpawn(entity);
        TeleportEntity(entity, pos, NULL_VECTOR, NULL_VECTOR);
        PrintToChat(client, "Test entity created successfully!");
        
        // 5秒后删除
        CreateTimer(5.0, Timer_RemoveEntity, EntIndexToEntRef(entity));
    }
    else
    {
        PrintToChat(client, "Failed to create test entity!");
    }
    
    return Plugin_Handled;
}

public Action Timer_RemoveEntity(Handle timer, int entityRef)
{
    int entity = EntRefToEntIndex(entityRef);
    if (entity != INVALID_ENT_REFERENCE && IsValidEntity(entity))
    {
        RemoveEntity(entity);
    }
    return Plugin_Stop;
}
